'use strict';

module.exports = ({ strapi }) => ({
  // Get KPI data for dashboard
  async getKpi(ctx) {
    try {
      // Mock data for now - replace with actual database queries
      const kpiData = {
        totalRevenue: 150000000, // 150 million VND
        totalOrders: 1250,
        totalUsers: 85,
        totalProducts: 320,
      };

      ctx.body = {
        data: kpiData,
      };
    } catch (error) {
      ctx.throw(500, `Failed to fetch KPI data: ${error.message}`);
    }
  },

  // Get revenue chart data
  async getRevenueChart(ctx) {
    try {
      // Mock data for now - replace with actual database queries
      const revenueData = [
        { month: 'T1', revenue: 12000000, orders: 95 },
        { month: 'T2', revenue: 15000000, orders: 120 },
        { month: 'T3', revenue: 18000000, orders: 140 },
        { month: 'T4', revenue: 14000000, orders: 110 },
        { month: 'T5', revenue: 20000000, orders: 160 },
        { month: 'T6', revenue: 22000000, orders: 175 },
        { month: 'T7', revenue: 25000000, orders: 200 },
        { month: 'T8', revenue: 23000000, orders: 185 },
        { month: 'T9', revenue: 27000000, orders: 210 },
        { month: 'T10', revenue: 30000000, orders: 240 },
        { month: 'T11', revenue: 28000000, orders: 220 },
        { month: 'T12', revenue: 32000000, orders: 250 },
      ];

      ctx.body = {
        data: revenueData,
      };
    } catch (error) {
      ctx.throw(500, `Failed to fetch revenue chart data: ${error.message}`);
    }
  },

  // Get top customers
  async getTopCustomers(ctx) {
    try {
      // Mock data for now - replace with actual database queries
      const topCustomers = [
        {
          id: 1,
          name: 'Nguyễn Văn A',
          email: '<EMAIL>',
          totalOrders: 25,
          totalSpent: 15000000,
        },
        {
          id: 2,
          name: 'Trần Thị B',
          email: '<EMAIL>',
          totalOrders: 18,
          totalSpent: 12000000,
        },
        {
          id: 3,
          name: 'Lê Văn C',
          email: '<EMAIL>',
          totalOrders: 15,
          totalSpent: 9500000,
        },
        {
          id: 4,
          name: 'Phạm Thị D',
          email: '<EMAIL>',
          totalOrders: 12,
          totalSpent: 8000000,
        },
        {
          id: 5,
          name: 'Hoàng Văn E',
          email: '<EMAIL>',
          totalOrders: 10,
          totalSpent: 6500000,
        },
      ];

      ctx.body = {
        data: topCustomers,
      };
    } catch (error) {
      ctx.throw(500, `Failed to fetch top customers: ${error.message}`);
    }
  },

  // Get best selling products
  async getBestSellingProducts(ctx) {
    try {
      // Mock data for now - replace with actual database queries
      const bestSellingProducts = [
        {
          id: 1,
          name: 'Sản phẩm A',
          sold: 150,
          revenue: 7500000,
        },
        {
          id: 2,
          name: 'Sản phẩm B',
          sold: 120,
          revenue: 6000000,
        },
        {
          id: 3,
          name: 'Sản phẩm C',
          sold: 95,
          revenue: 4750000,
        },
        {
          id: 4,
          name: 'Sản phẩm D',
          sold: 80,
          revenue: 4000000,
        },
        {
          id: 5,
          name: 'Sản phẩm E',
          sold: 65,
          revenue: 3250000,
        },
      ];

      ctx.body = {
        data: bestSellingProducts,
      };
    } catch (error) {
      ctx.throw(500, `Failed to fetch best selling products: ${error.message}`);
    }
  },
});
