'use strict';

module.exports = ({ strapi }) => ({
  // Get orders with filtering and pagination
  async getOrders(ctx) {
    try {
      const { 
        page = 1, 
        pageSize = 10, 
        status, 
        sortBy = 'createdAt', 
        sortOrder = 'desc' 
      } = ctx.query;

      // Mock data for now - replace with actual database queries
      const mockOrders = [
        {
          id: 1,
          code: 'DH001',
          customer: {
            name: '<PERSON>uyễ<PERSON>ăn A',
            phone: '0901234567',
          },
          priceAfterTax: 1500000,
          statusOrder: 'Chờ xác nhận',
          products: [
            { id: 1, name: 'Sản phẩm A' },
            { id: 2, name: 'Sản phẩm B' },
          ],
          createdAt: new Date('2025-01-25T10:30:00Z').toISOString(),
        },
        {
          id: 2,
          code: 'DH002',
          customer: {
            name: 'Trần Thị B',
            phone: '0901234568',
          },
          priceAfterTax: 2200000,
          statusOrder: 'Chờ giao hàng',
          products: [
            { id: 3, name: 'Sản phẩm C' },
          ],
          createdAt: new Date('2025-01-24T14:15:00Z').toISOString(),
        },
        {
          id: 3,
          code: 'DH003',
          customer: {
            name: 'Lê Văn C',
            phone: '0901234569',
          },
          priceAfterTax: 850000,
          statusOrder: 'Đã hoàn thành',
          products: [
            { id: 4, name: 'Sản phẩm D' },
            { id: 5, name: 'Sản phẩm E' },
            { id: 6, name: 'Sản phẩm F' },
          ],
          createdAt: new Date('2025-01-23T09:45:00Z').toISOString(),
        },
        {
          id: 4,
          code: 'DH004',
          customer: {
            name: 'Phạm Thị D',
            phone: '0901234570',
          },
          priceAfterTax: 3200000,
          statusOrder: 'Chờ xác nhận',
          products: [
            { id: 7, name: 'Sản phẩm G' },
          ],
          createdAt: new Date('2025-01-22T16:20:00Z').toISOString(),
        },
        {
          id: 5,
          code: 'DH005',
          customer: {
            name: 'Hoàng Văn E',
            phone: '0901234571',
          },
          priceAfterTax: 1800000,
          statusOrder: 'Đang giao hàng',
          products: [
            { id: 8, name: 'Sản phẩm H' },
            { id: 9, name: 'Sản phẩm I' },
          ],
          createdAt: new Date('2025-01-21T11:10:00Z').toISOString(),
        },
      ];

      // Filter by status if provided
      let filteredOrders = mockOrders;
      if (status) {
        filteredOrders = mockOrders.filter(order => order.statusOrder === status);
      }

      // Sort orders
      filteredOrders.sort((a, b) => {
        const aValue = a[sortBy];
        const bValue = b[sortBy];
        
        if (sortOrder === 'desc') {
          return new Date(bValue) - new Date(aValue);
        } else {
          return new Date(aValue) - new Date(bValue);
        }
      });

      // Pagination
      const startIndex = (page - 1) * pageSize;
      const endIndex = startIndex + parseInt(pageSize);
      const paginatedOrders = filteredOrders.slice(startIndex, endIndex);

      ctx.body = {
        data: paginatedOrders,
        meta: {
          pagination: {
            page: parseInt(page),
            pageSize: parseInt(pageSize),
            total: filteredOrders.length,
            pageCount: Math.ceil(filteredOrders.length / pageSize),
          },
        },
      };
    } catch (error) {
      ctx.throw(500, `Failed to fetch orders: ${error.message}`);
    }
  },
});
