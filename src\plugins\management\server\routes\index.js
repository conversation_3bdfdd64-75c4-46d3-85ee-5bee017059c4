module.exports = [
  {
    method: 'GET',
    path: '/',
    handler: 'myController.index',
    config: {
      policies: [],
    },
  },
  // Dashboard routes
  {
    method: 'GET',
    path: '/dashboard/kpi',
    handler: 'dashboardController.getKpi',
    config: {
      policies: [],
    },
  },
  {
    method: 'GET',
    path: '/dashboard/charts/revenue',
    handler: 'dashboardController.getRevenueChart',
    config: {
      policies: [],
    },
  },
  {
    method: 'GET',
    path: '/dashboard/top-customers',
    handler: 'dashboardController.getTopCustomers',
    config: {
      policies: [],
    },
  },
  {
    method: 'GET',
    path: '/dashboard/best-selling-products',
    handler: 'dashboardController.getBestSellingProducts',
    config: {
      policies: [],
    },
  },
  // Orders routes
  {
    method: 'GET',
    path: '/orders',
    handler: 'ordersController.getOrders',
    config: {
      policies: [],
    },
  },
];
